'use client'

import { trackEventCreation } from "@/components/GoogleAnalytics";
import Container from "@/components/Container";
import { EventFormSkeleton } from "@/components/EditEventSkeleton";
import { DatePicker } from "@/components/DatePicker";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Form, FormDescription, FormField, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useEvent } from "@/hooks/useEvent";
import { Event } from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { ChevronDown, AlertTriangle, Sparkles, Loader2 as LoaderIcon, Check } from "lucide-react";
import RemoteConfig from "@/lib/remoteConfig";
import { useForm } from "react-hook-form";
import { useSession } from "next-auth/react";
import { z } from "zod";
import { ProtectedLayout } from "@/components/layouts/ProtectedLayout";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { plans, Plan } from "@/lib/plans"
import { Header } from "@/components/Header";
import { TimezoneCombobox } from "@/components/timezone-combobox";
import { isEventLocked, getEventLockMessage } from "@/lib/event/eventLock";
import { getUserTimezone } from "@/lib/dayjs";

// Message style options
const messageStyleOptions = [
  { value: 'personalised', label: 'Personalised Message', description: 'Tailored and personal touch' },
  { value: 'casual_friendly', label: 'Casual & Friendly', description: 'Relaxed and approachable' },
  { value: 'formal_professional', label: 'Formal & Professional', description: 'Polished and professional' },
  { value: 'fun_energetic', label: 'Fun & Energetic', description: 'Playful and exciting' },
  { value: 'business_professional', label: 'Business Professional', description: 'Corporate and formal' },
  { value: 'creative_unique', label: 'Creative & Unique', description: 'Original and innovative' },
];

// Function to get timezone offset string (e.g., UTC+10:00, UTC-7:00) and numerical offset in minutes
const getTimezoneInfo = (timezone: string): { offsetString: string, offsetMinutes: number } => {
  const now = new Date();
  const tzOffset = now.toLocaleString('en-US', { timeZone: timezone, timeZoneName: 'short' })
    .split(' ').pop() || '';
  
  // Calculate the offset in minutes for sorting
  const offsetMinutes = -(new Date(now.toLocaleString('en-US', { timeZone: 'UTC' })).getTime() - 
                         new Date(now.toLocaleString('en-US', { timeZone: timezone })).getTime()) / 1000 / 60;
  
  // If timezone abbreviation doesn't include offset details, calculate it manually for display
  if (!tzOffset.includes('+') && !tzOffset.includes('-')) {
    const offsetHours = Math.floor(Math.abs(offsetMinutes) / 60);
    const offsetMinutesPart = Math.floor(Math.abs(offsetMinutes) % 60);
    const sign = offsetMinutes < 0 ? '-' : '+';
    return {
      offsetString: `(UTC${sign}${offsetHours.toString().padStart(2, '0')}:${offsetMinutesPart.toString().padStart(2, '0')})`,
      offsetMinutes
    };
  }
  
  return {
    offsetString: `(${tzOffset})`,
    offsetMinutes
  };
};

// Create timezone objects with name, offset string, and numerical offset
const timezoneObjects = [
  ...Intl.supportedValuesOf('timeZone')
    .map(tz => ({
      name: tz,
      ...getTimezoneInfo(tz)
    }))
    // Sort by offsetMinutes (descending), then by name
    .sort((a, b) => {
      // First sort by offset (descending, so UTC+14 comes first, UTC-12 last)
      if (a.offsetMinutes !== b.offsetMinutes) {
        return b.offsetMinutes - a.offsetMinutes;
      }
      
      // If offsets are the same, sort by name
      return a.name.localeCompare(b.name);
    }).reverse()
];

// Helper function to calculate the default RSVP due date (11:59 PM the day before event date)
const calculateDefaultRSVPDueDate = (eventDate: Date, timezone?: string): Date => {
  const tz = timezone || getUserTimezone();
  // Create a date object for the event date
  const date = new Date(eventDate);

  // Subtract one day to get the day before
  date.setDate(date.getDate() - 1);

  // Set the time to 11:59 PM (23:59:00)
  date.setHours(23, 59, 0, 0);

  return date;
};

const EventFormSchema = z.object({
  id: z.string().optional(),
  eventName: z.string().min(1),
  eventDate: z.date().refine(date => {
    // Ensure event date is not in the past
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date >= today;
  }, { message: "Event date cannot be in the past" }),
  start: z.string(),
  end: z.string().optional(),
  location: z.string(),
  timezone: z.string().default('Australia/Melbourne'),
  rsvpDueDate: z.date().optional().refine(date => {
    if (!date) return true; // Optional, so null/undefined is fine

    // Ensure RSVP due date is not in the past
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date >= today;
  }, { message: "RSVP due date cannot be in the past" }),
  rsvpDueTime: z.string().optional(),
  message: z.string().optional(),
  messageStyle: z.enum(['personalised', 'casual_friendly', 'formal_professional', 'fun_energetic', 'business_professional', 'creative_unique']).optional().default('personalised'),
  host: z.string().min(1).max(256),
  plan: z.enum(['free', 'host_plus', 'host_pro']),
  status: z.enum(['pending_payment', 'active']).optional()
}).refine(data => {
  // Skip validation if either field is missing
  if (!data.eventDate || !data.rsvpDueDate) return true;

  // Ensure RSVP due date is not after event date
  return data.rsvpDueDate <= data.eventDate;
}, {
  message: "RSVP due date cannot be after the event date",
  path: ["rsvpDueDate"] // Show error on the rsvpDueDate field
})

function EditEvent() {
  const router = useRouter();
  const { data: session } = useSession();
  const { eventId, plan: selectedPlan } = router.query;
  const isNew = !eventId || eventId === 'new';
  const [isBetaMode, setIsBetaMode] = useState(false);
  const form = useForm<z.infer<typeof EventFormSchema>>({
    resolver: zodResolver(EventFormSchema),
    defaultValues: {
      eventName: "",
      eventDate: new Date(),
      start: "09:00",
      end: "17:00",
      location: "",
      timezone: getUserTimezone(), // Use user's detected timezone      // Default RSVP due date to midnight before event date
      rsvpDueDate: calculateDefaultRSVPDueDate(new Date(), getUserTimezone()),
      rsvpDueTime: "23:59", // Default to 11:59 PM
      message: "",
      messageStyle: "personalised",
      host: session?.user?.name || "",
      plan: selectedPlan as 'free' | 'host_plus' | 'host_pro' || 'free',
      status: selectedPlan === 'free' ? 'active' : 'pending_payment'
    }
  });
  const { event, saveEvent, loading, error } = useEvent(eventId as string);
  const [redirect, setRedirect] = useState(false);
  const [isEnhancingMessage, setIsEnhancingMessage] = useState(false);

  // AI Enhancement function for message
  const enhanceMessageWithAI = async () => {
    const formData = form.getValues();
    
    // Validate required fields
    if (!formData.eventName) {
      form.setError('eventName', {
        type: 'manual',
        message: 'Event title is required for AI enhancement'
      });
      return;
    }

    setIsEnhancingMessage(true);
    
    try {      
      const response = await fetch('/api/ai/enhance-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventName: formData.eventName,
          eventDate: formData.eventDate,
          startTime: formData.start,
          endTime: formData.end,
          location: formData.location,
          timezone: formData.timezone,
          rsvpDueDate: formData.rsvpDueDate,
          rsvpDueTime: formData.rsvpDueTime,
          host: formData.host,
          currentMessage: formData.message,
          messageStyle: formData.messageStyle,
        }),
      });
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to enhance message');
      }

      // Update the message field with the enhanced version
      form.setValue('message', data.enhancedMessage);
      
    } catch (error) {
      console.error('Error enhancing message:', error);
      // Could add a toast notification here
    } finally {
      setIsEnhancingMessage(false);
    }
  };

  const onSubmit = form.handleSubmit(async (data) => {
    // Check for validation errors
    if(Object.keys(form.formState.errors).length > 0) return;

    // Additional validation for dates
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Ensure event date is not in the past
    if (data.eventDate < today) {
      form.setError('eventDate', {
        type: 'manual',
        message: 'Event date cannot be in the past'
      });
      return;
    }

    // Ensure RSVP due date is not in the past and not after event date
    if (data.rsvpDueDate) {
      if (data.rsvpDueDate < today) {
        form.setError('rsvpDueDate', {
          type: 'manual',
          message: 'RSVP due date cannot be in the past'
        });
        return;
      }

      if (data.rsvpDueDate > data.eventDate) {
        form.setError('rsvpDueDate', {
          type: 'manual',
          message: 'RSVP due date cannot be after the event date'
        });
        return;
      }
    }

    // Determine if payment is required based on plan change and current status
    const needsPayment = () => {
      // For new events, payment is needed for non-free plans
      if (isNew) {
        return data.plan !== 'free';
      }
      
      // For existing events:
      // 1. If current status is 'active', only need payment if upgrading plan
      if (event?.status === 'active') {
        // Define plan tiers for comparison (higher number = more premium)
        const planTiers = { 'free': 0, 'host_plus': 1, 'host_pro': 2 };

        if (!event.plan || !data.plan) return false;
        // Only need payment if upgrading to a more premium plan
        return planTiers[data.plan] > planTiers[event.plan];
      }
      
      // 2. If current status is 'pending_payment', still need payment for non-free plans
      return data.plan !== 'free';
    };

    // Set the appropriate status based on current conditions
    const newStatus = data.plan === 'free' ? 'active' : 
      (event?.status === 'active' && data.plan === event.plan) ? 'active' : 
      needsPayment() ? 'pending_payment' : 'active';

    // Combine RSVP due date and time into a single Date object
    let rsvpDueDate = data.rsvpDueDate;
    if (rsvpDueDate && data.rsvpDueTime) {
      const [hours, minutes] = data.rsvpDueTime.split(':').map(Number);
      rsvpDueDate = new Date(rsvpDueDate);
      rsvpDueDate.setHours(hours, minutes, 0, 0);
    }

    const saveData: Event = {
      ...data,
      ID: eventId as string,
      end: data.end || "",
      start: data.start || "",
      message: data.message || "",
      managers: event?.managers || [],
      ownerAccountId: session?.user?.id || "",
      ownerEmail: session?.user?.email || "",
      status: newStatus,
      organizationId: event?.organizationId || "",
      rsvpDueDate: rsvpDueDate
    };

    const savedEvent = await saveEvent(saveData);
    
    if (savedEvent) {
      // Track event creation for analytics
      const isPaid = data.plan !== 'free';
      trackEventCreation(savedEvent.ID, isPaid);
      
      if (needsPayment()) {
        // If upgrading from a paid plan to another paid plan, pass the current plan for differential pricing
        const queryParams = new URLSearchParams({
          eventId: savedEvent.ID,
          plan: data.plan
        });
        
        // If upgrading from an existing paid plan, add the from_plan parameter
        if (!isNew && event?.plan && event.plan !== 'free' && event.status === 'active') {
          queryParams.append('from_plan', event.plan);
        }
        
        router.push(`/payment?${queryParams.toString()}`);
      } else {
        router.push('/event/' + savedEvent.ID);
      }
    }
  }, (errors) => {
    console.log(errors);
  });
  
  useEffect(() => {
    if(event && event.ID && event.ID !== 'new') {
      // If the event is locked (RSVP date has passed), redirect to event page
      if (isEventLocked(event) && !isNew) {
        router.push('/event/'+ event.ID);
        return;
      }
      
      // Otherwise, only redirect if the redirect flag is set
      if (redirect) {
        console.log('event', event);
        router.push('/event/'+ event.ID);
      }
    }

    console.log({
      event,
      loading,
      error,
    });
    
  }, [loading, error, event, router, redirect, isNew]);

  useEffect(() => {
    if(event && event.ID && event.ID !== 'new') {
      const eventDate = new Date(event.eventDate);
      console.log('eventDate', eventDate);

      // If the event has a host name, use it; otherwise, use the session user's name
      const hostName = event.host || session?.user?.name || "";

      // If the event has an RSVP due date, use it; otherwise, calculate it from the event date
      const rsvpDueDate = event.rsvpDueDate
        ? new Date(event.rsvpDueDate)
        : calculateDefaultRSVPDueDate(eventDate, event.timezone);

      // Extract time from RSVP due date or use default
      let rsvpDueTime = "23:59"; // Default to 11:59 PM
      if (event.rsvpDueDate) {
        const dueDate = new Date(event.rsvpDueDate);
        const hours = dueDate.getHours().toString().padStart(2, '0');
        const minutes = dueDate.getMinutes().toString().padStart(2, '0');
        rsvpDueTime = `${hours}:${minutes}`;
      }

      form.reset({
        ...event,
        eventDate: eventDate,
        rsvpDueDate: rsvpDueDate,
        rsvpDueTime: rsvpDueTime,
        host: hostName
      });
    }
  }, [event, form, session]);

  // log values of form on change
  useEffect(() => {
    console.log('values', form.getValues());
  }, [form, form.formState.dirtyFields]);
  
  useEffect(() => {
    console.log('errors', form.formState.errors);
  }, [form.formState.errors]);

  // Update host name when session data becomes available
  useEffect(() => {
    if (session?.user?.name) {
      // Only set the host name if it's a new event or if the host field is empty
      if (isNew || !form.getValues().host) {
        form.setValue('host', session.user.name);
      }
    }
  }, [session, form, isNew]);

  // Update RSVP due date when event date changes
  useEffect(() => {
    const subscription = form.watch((_, { name }) => {
      // If the event date or timezone changes, update the RSVP due date
      if (name === 'eventDate' || name === 'timezone') {
        const eventDate = form.getValues('eventDate');
        const timezone = form.getValues('timezone');
        const currentRsvpDueDate = form.getValues('rsvpDueDate');

        // Only update if the user hasn't manually changed the RSVP due date
        if (eventDate && !form.formState.dirtyFields.rsvpDueDate) {
          form.setValue('rsvpDueDate', calculateDefaultRSVPDueDate(eventDate, timezone));
        }
        // If RSVP due date is after event date, adjust it to be on or before event date
        else if (eventDate && currentRsvpDueDate && currentRsvpDueDate > eventDate) {
          form.setValue('rsvpDueDate', calculateDefaultRSVPDueDate(eventDate, timezone));
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  // Check if beta mode is enabled
  useEffect(() => {
    const checkBetaMode = async () => {
      const remoteConfig = RemoteConfig.getInstance();
      const betaMode = remoteConfig.isBetaMode();
      setIsBetaMode(betaMode);

      // If in beta mode, set the plan to free and status to active
      if (betaMode) {
        form.setValue('plan', 'free');
        form.setValue('status', 'active');
      }
    };

    checkBetaMode();
  }, [form]);

  return (
    <ProtectedLayout>
      <div className="flex flex-col  bg-gray-50">
        {/* Use the shared Header component with breadcrumbs */}
        <Header 
          title={isNew ? "Create Event" : "Edit Event"}
          breadcrumbs={[
            { label: 'Events', href: '/events' },
            ...(isNew ? [] : [{ label: event?.eventName || 'Event Details', href: `/event/${eventId}` }])
          ]}
        />
        
        <div className="flex-1 p-4 pb-20">
          <div className="container mx-auto">
            {eventId !== "new" && (
              <div className="mb-4">
                <Badge variant="outline">
                  ID: {eventId}
                </Badge>
              </div>
            )}
            {/* Show warning if event is locked */}
            {event && isEventLocked(event) && !isNew && (
              <div className="mb-4 bg-yellow-50 border border-yellow-300 rounded-md p-4">
                <div className="flex items-start">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-semibold text-yellow-800">Event Locked</h3>
                    <p className="text-sm text-yellow-700">{getEventLockMessage(event)}</p>
                    <p className="text-sm text-yellow-700 mt-1">You will be redirected to the event page.</p>
                  </div>
                </div>
              </div>
            )}
            {loading ? (
              <EventFormSkeleton />
            ) : (
              <Card>
                <CardHeader>
                  <h2 className="text-xl font-semibold">Event plan</h2>
                </CardHeader>
                <CardContent>
                  <Form {...form}>
                    <form onSubmit={onSubmit}>
                      <div className="flex flex-col gap-3">
                        {isBetaMode && (
                          <div className="bg-yellow-50 border border-yellow-100 rounded-md p-4 mb-4">
                            <p className="text-sm">During the Beta, you’ll get the $35 Host Pro plan for free on all your events.</p>
                          </div>
                        )}
                        <div className="space-y-2">
                          {/* Beta mode UI - Single Host plan */}
                          {isBetaMode ? (
                            <div className="border border-rose-500 rounded-md p-4">
                              <div className="flex justify-between items-center">
                                <div>
                                  <h3 className="font-semibold">Host Pro</h3>
                                  <p className="text-sm text-gray-500">Limited to 50 invites</p>
                                </div>
                                <div className="text-right">
                                  <p className="text-sm">
                                    <span className="line-through text-gray-400 mr-1">A$ 35</span>
                                    <span className="text-rose-500 font-semibold">(Free)</span>
                                  </p>
                                  <p className="text-xs text-gray-500">/event</p>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <>
                              {/* Desktop view - RadioGroup */}
                              <div className="hidden md:block">
                                <RadioGroup
                                  defaultValue={selectedPlan as string || event?.plan || 'free'}
                                  onValueChange={(value) => {
                                    form.setValue('plan', value as 'free' | 'host_plus' | 'host_pro');
                                    form.setValue('status', value === 'free' ? 'active' : 'pending_payment');
                                  }}
                                  className="grid grid-cols-1 md:grid-cols-3 gap-4"
                                >
                                  {plans.map((plan: Plan) => (
                                    // Skip the Gatsby plan in both create and edit event pages
                                    plan.priceId !== 'the_gatsby' ? (
                                      <div key={plan.priceId}>
                                        <RadioGroupItem
                                          value={plan.priceId}
                                          id={plan.priceId}
                                          className="peer sr-only"
                                        />
                                        <Label
                                          htmlFor={plan.priceId}
                                          className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-rose-500 [&:has([data-state=checked])]:border-rose-500"
                                        >
                                          <div className="space-y-2 text-center w-full">
                                            <p className="text-xl font-semibold">{plan.name}</p>
                                            <p className="text-sm text-gray-600">{plan.price}/Event</p>
                                            {plan.features && plan.features.length > 0 && (
                                              <div className="mt-3">
                                                <Badge variant="outline"
                                                  className="bg-[#FFF3EB] text-muted-foreground font-normal rounded-full px-3 py-1 text-xs border border-rose-200"
                                                >
                                                  {plan.features[0]}
                                                </Badge>
                                              </div>
                                            )}
                                          </div>
                                        </Label>
                                      </div>
                                    ) : null
                                  ))}
                                </RadioGroup>
                              </div>

                              {/* Mobile and tablet view - Dropdown */}
                              <div className="md:hidden">
                                <div className="mb-2">
                                  {/* Current selected plan display */}
                                  <div className="rounded-md border border-rose-200 p-4 mb-2">
                                    {(() => {
                                      const currentPlanId = form.watch('plan') || 'free';
                                      const currentPlan = plans.find(p => p.priceId === currentPlanId);
                                      return (
                                        <div className="flex flex-col">
                                          <div className="flex justify-between items-center">
                                            <p className="text-lg font-semibold">{currentPlan?.name}</p>
                                            <p className="text-sm text-gray-600">{currentPlan?.price}/Event</p>
                                          </div>
                                          {currentPlan?.features && currentPlan.features.length > 0 && (
                                            <div className="mt-2">
                                              <p className="text-xs text-gray-500">{currentPlan.features[0]}</p>
                                            </div>
                                          )}
                                        </div>
                                      );
                                    })()}
                                  </div>

                                  {/* Dropdown for plan selection */}
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="outline" className="w-full justify-between">
                                        Select Plans
                                        <ChevronDown className="h-4 w-4 opacity-50" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent className="w-full" align="start" style={{ width: 'var(--radix-dropdown-menu-trigger-width)' }}>
                                      {plans.map((plan) => (
                                        // Skip the Gatsby plan
                                        plan.priceId !== 'the_gatsby' ? (
                                          <DropdownMenuItem
                                            key={plan.priceId}
                                            className="flex flex-col items-start py-3 cursor-pointer"
                                            onClick={() => {
                                              form.setValue('plan', plan.priceId as 'free' | 'host_plus' | 'host_pro');
                                              form.setValue('status', plan.priceId === 'free' ? 'active' : 'pending_payment');
                                            }}
                                          >
                                            <div className="flex justify-between items-center w-full">
                                              <span className="font-medium">{plan.name}</span>
                                              <span className="text-sm text-gray-600">{plan.price}/Event</span>
                                            </div>
                                            {plan.features && plan.features.length > 0 && (
                                              <span className="text-xs text-gray-500 mt-1">{plan.features[0]}</span>
                                            )}
                                          </DropdownMenuItem>
                                        ) : null
                                      ))}
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              </div>

                              <div className="mt-2 text-muted-foreground font-small text-sm">
                                All plans are free during the Beta period
                              </div>
                            </>
                          )}
                        </div>

                        <div className="mt-6">
                          <h2 className="text-xl font-semibold">Event Details</h2>
                        </div>

                        <FormField
                          control={form.control}
                          name="eventName"
                          render={({field}) => (
                            <div>
                              <Label htmlFor="eventDate" className="text-foreground">Event Title</Label>
                              <Input type="text" required placeholder="Event Name" {...field} />
                            </div>
                          )}
                        />
                        
                        {/* Date and Timezone in a single row */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <FormField 
                              control={form.control}
                              name="eventDate"
                              render={({field}) => (
                                <div>
                                  <Label htmlFor="eventDate" className="text-foreground">Date</Label>
                                  <DatePicker
                                    {...field}
                                    value={field.value}
                                    fromDate={new Date()} // Restrict to today and future dates
                                    onChange={(date) => {
                                      // Clear any previous errors when date is changed
                                      form.clearErrors('eventDate');

                                      // Apply the new date
                                      field.onChange(date);

                                      // Validate against today's date
                                      const today = new Date();
                                      today.setHours(0, 0, 0, 0);
                                      if (date && date < today) {
                                        form.setError('eventDate', {
                                          type: 'manual',
                                          message: 'Event date cannot be in the past'
                                        });
                                      }

                                      // Also validate RSVP due date against the new event date
                                      const rsvpDueDate = form.getValues('rsvpDueDate');
                                      if (date && rsvpDueDate && rsvpDueDate > date) {
                                        // Update RSVP due date to be the day before event
                                        form.setValue('rsvpDueDate', calculateDefaultRSVPDueDate(date, form.getValues('timezone')));
                                      }
                                    }}
                                  />
                                  <p className="text-xs text-gray-500 mt-1">
                                    You can only select today or future dates for events.
                                  </p>
                                  {form.formState.errors.eventDate && (
                                    <FormMessage>{form.formState.errors.eventDate.message}</FormMessage>
                                  )}
                                </div>
                              )}
                            />
                          </div>
                          <div>
                            <FormField
                              control={form.control}
                              name="timezone"
                              render={({field}) => (
                                <div>
                                  <Label htmlFor="timezone" className="text-gray-400">Timezone</Label>
                                  <TimezoneCombobox
                                    timezones={timezoneObjects}
                                    value={field.value}
                                    onValueChange={field.onChange}
                                    placeholder="Select timezone"
                                  />
                                </div>
                              )}
                            />
                          </div>
                        </div>

                        {/* RSVP Due Date and Time */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <FormField
                              control={form.control}
                              name="rsvpDueDate"
                              render={({field}) => (
                                <div>
                                  <Label htmlFor="rsvpDueDate" className="text-foreground">RSVP Due Date</Label>
                                  <div className="flex flex-col space-y-1">
                                    <DatePicker
                                      {...field}
                                      value={field.value}
                                      fromDate={new Date()} // Restrict to today and future dates
                                      toDate={form.getValues('eventDate')} // Cannot be after event date
                                      onChange={(date) => {
                                        // Clear any previous errors when date is changed
                                        form.clearErrors('rsvpDueDate');

                                        // Apply the new date
                                        field.onChange(date);

                                        // Validate against event date
                                        const eventDate = form.getValues('eventDate');
                                        if (date && eventDate && date > eventDate) {
                                          form.setError('rsvpDueDate', {
                                            type: 'manual',
                                            message: 'RSVP due date cannot be after the event date'
                                          });
                                        }
                                      }}
                                    />
                                  </div>
                                  <p className="text-xs text-gray-500 mt-1">
                                    RSVP due date cannot be after the event date.
                                  </p>
                                  {form.formState.errors.rsvpDueDate && (
                                    <FormMessage>{form.formState.errors.rsvpDueDate.message}</FormMessage>
                                  )}
                                </div>
                              )}
                            />
                          </div>
                          <div>
                            <FormField
                              control={form.control}
                              name="rsvpDueTime"
                              render={({field}) => (
                                <div>
                                  <Label htmlFor="rsvpDueTime" className="text-foreground">RSVP Due Time</Label>
                                  <Input type="time" placeholder="Due Time" className="w-full" {...field} />
                                </div>
                              )}
                            />
                          </div>
                          <div className="md:col-span-2">
                            <p className="text-xs text-gray-500">
                              Guests will not be able to RSVP after this date and time. Defaults to 11:59 PM the day before the event.
                            </p>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <FormField
                              control={form.control}
                              name="start"
                              render={({field}) => (
                                <div>
                                  <Label htmlFor="start" className="text-foreground">Start Time</Label>
                                  <Input type="time" placeholder="Start Time" className="w-full" {...field} />
                                </div>
                              )}
                            />
                          </div>
                          <div>
                            <FormField
                              control={form.control}
                              name="end"
                              render={({field}) => (
                                <div>
                                  <Label htmlFor="end" className="text-gray-400">End Time</Label>
                                  <Input type="time" placeholder="End Time" className="w-full" {...field} />
                                </div>
                              )}
                            />
                          </div>
                        </div>
                        <FormField 
                          control={form.control}
                          name="location"
                          render={({field}) => (
                            <div>
                              <Label htmlFor="location" className="text-foreground">Venue Address</Label>
                              <Input type="text" placeholder="Address" {...field} />
                            </div>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="message"
                          render={({field}) => (
                            <div>                              <div className="flex items-center justify-between mb-2">
                                <Label htmlFor="message" className="text-foreground">Message for Guests (Optional)</Label>
                                {session?.user?.hasAiAccess && (
                                  <div className="flex items-center">
                                    <Button
                                      type="button"
                                      variant="primary-button"
                                      size="sm"
                                      onClick={enhanceMessageWithAI}
                                      disabled={isEnhancingMessage || !form.watch('eventName')}
                                      className="gap-2 rounded-r-none"
                                    >
                                      {isEnhancingMessage ? (
                                        <>
                                          <LoaderIcon className="w-4 h-4 animate-spin" />
                                          Enhancing...
                                        </>
                                      ) : (
                                        <>
                                          <Sparkles className="w-4 h-4" />
                                          Enhance with AI
                                        </>
                                      )}
                                    </Button>

                                    {/* Separator */}
                                    <div className="w-px h-8 bg-white" />

                                    <DropdownMenu>
                                      <DropdownMenuTrigger asChild>
                                        <Button 
                                          type="button"
                                          variant="primary-button"
                                          size="sm"
                                          className="w-8 rounded-l-none border-none shadow-none p-0 focus:outline-none focus:ring-0 focus-visible:ring-0"
                                          disabled={isEnhancingMessage || !form.watch('eventName')}
                                        >
                                          <ChevronDown className="w-4 h-4" />
                                        </Button>
                                      </DropdownMenuTrigger>                                      <DropdownMenuContent className="w-56" align="end">
                                        {messageStyleOptions.map((style) => {
                                          const isSelected = form.watch('messageStyle') === style.value;
                                          return (
                                            <DropdownMenuItem
                                              key={style.value}
                                              onClick={() => form.setValue('messageStyle', style.value as 'personalised' | 'casual_friendly' | 'formal_professional' | 'fun_energetic' | 'business_professional' | 'creative_unique')}
                                              className="cursor-pointer"
                                            >
                                              <div className="flex items-center justify-between w-full">
                                                <div className="flex flex-col gap-1">
                                                  <span className="font-medium">{style.label}</span>
                                                  <span className="text-xs text-gray-500">{style.description}</span>
                                                </div>
                                                {isSelected && (
                                                  <Check className="w-4 h-4 text-blue-600" />
                                                )}
                                              </div>
                                            </DropdownMenuItem>
                                          );
                                        })}
                                      </DropdownMenuContent>
                                    </DropdownMenu>
                                  </div>
                                )}
                              </div>
                              <Textarea 
                                placeholder="Message (optional)" 
                                {...field} 
                                className="min-h-[100px]"
                              />
                              <FormDescription>
                                Let your guests know what to expect - share important details, dress code, surprises, or anything you&apos;d like them to keep in mind for the event.
                              </FormDescription>
                            </div>
                          )}
                        />
                        <div className="mt-6">
                          <h2 className="text-xl font-semibold">Host Details</h2>
                        </div>

                        <FormField 
                          control={form.control}
                          name="host"
                          render={({field}) => (
                            <div>
                              <Label htmlFor="host" className="text-foreground">Your Name</Label>
                              <Input type="text" {...field} />
                              <FormDescription>This name will be displayed on invites as host for this event.</FormDescription>
                              {form.getFieldState(field.name).error && <FormMessage>{form.getFieldState(field.name).error?.message}</FormMessage>}
                            </div>
                          )}
                        />
                        {/* <FormField 
                          control={form.control}
                          name="ownerEmail"
                          render={({field}) => (
                            <div className="mb-8">
                              <Label htmlFor="ownerEmail" className="text-gray-400">Your Email Address</Label>
                              <Input type="text" {...field} />
                              <FormDescription>Enter your email address to receive RSVP notifications and Updates for this event</FormDescription>
                              {form.getFieldState(field.name).error && <FormMessage>{form.getFieldState(field.name).error?.message}</FormMessage>}
                            </div>
                          )}
                        /> */}
                        <Button variant="primary-button" className="" size={"lg"}>{isNew ? 'Create Event' : 'Save Changes'}</Button>
                        <Button variant="outline" onClick={() => {
                          if(isNew) {
                            router.push('/');
                            return;
                          }
                          router.push('/event/'+ eventId);
                        }}>Cancel</Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </ProtectedLayout>
  );
}

export default EditEvent;