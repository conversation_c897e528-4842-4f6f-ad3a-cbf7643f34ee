import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Calendar, Loader2, Mail, AlertTriangle, Sparkles } from "lucide-react";
import { Event, EventInvite, EventInviteListItem } from "@/types";
import { FormatDate } from "@/lib/dayjs";
import { useEffect, useState } from "react";
import { useToast } from "@/components/ui/use-toast";
import { isReminderLocked, getReminderLockMessage } from "@/lib/event/eventLock";
import { useSession } from "next-auth/react";

interface SendReminderDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  event: Event;
  totalInvites: number;
  invites: EventInvite[] | EventInviteListItem[];
}

export default function SendReminderDialog({
  open,
  onOpenChange,
  event,
  totalInvites,
  invites
}: SendReminderDialogProps) {
  // Get the event timezone
  const eventTimezone = event?.timezone || "Australia/Melbourne";
  const { toast } = useToast();
  const { data: session } = useSession();
  const [sending, setSending] = useState(false);
  const [generatingMessage, setGeneratingMessage] = useState(false);
  const [invitesWithEmail, setInvitesWithEmail] = useState<number>(0);

  // Count invites that have emails and are not declined
  useEffect(() => {
    if (invites && invites.length > 0) {
      const count = invites.filter(invite => {
        // Check if email exists and is not empty
        const hasEmail = 'email' in invite && invite.email && invite.email.trim() !== '';
        // Check if status is not declined
        return hasEmail && invite.status !== 'declined';
      }).length;

      setInvitesWithEmail(count);
    } else {
      setInvitesWithEmail(0);
    }
  }, [invites]);

  // Initialize with empty message
  const [message, setMessage] = useState('');

  const handleCancel = () => {
    onOpenChange(false);
  };
  const handleGenerateMessage = async () => {
    if (!event?.message) {
      toast({
        title: "No event message found",
        description: "This event doesn't have an original message to base the reminder on.",
        variant: "destructive"
      });
      return;
    }

    setGeneratingMessage(true);
    try {
      const response = await fetch(`/api/event/${event.ID}/generate-reminder-message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate reminder message');
      }

      const data = await response.json();
      setMessage(data.reminderMessage);
      
      toast({
        title: "Message generated!",
        description: "AI has created a reminder message based on your event details.",
      });
    } catch (error) {
      console.error('Error generating reminder message:', error);
      toast({
        title: "Error generating message",
        description: error instanceof Error ? error.message : 'Failed to generate reminder message',
        variant: "destructive"
      });
    } finally {
      setGeneratingMessage(false);
    }
  };

  const handleSendReminder = async () => {
    if (invitesWithEmail === 0) {
      toast({
        title: "No valid recipients",
        description: "There are no invites with email addresses to send reminders to.",
        variant: "destructive"
      });
      return;
    }

    setSending(true);
    try {
      const response = await fetch(`/api/event/${event.ID}/send-reminders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send reminders');
      }

      const data = await response.json();

      toast({
        title: "Reminders sent!",
        description: `Sent ${data.sentCount} reminder emails successfully.`,
      });

      onOpenChange(false);
    } catch (error) {
      console.error('Error sending reminders:', error);
      toast({
        title: "Error sending reminders",
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: "destructive"
      });
    } finally {
      setSending(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl">Send Reminders</DialogTitle>
          <DialogDescription>
            Reminders will be sent to guests with email addresses
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-5">
          {/* Event date passed warning */}
          {isReminderLocked(event) && (
            <div className="bg-red-50 border border-red-300 rounded-md p-3 mb-2">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-red-600 mr-2 mt-0.5 flex-shrink-0" />
                <div>
                  <h3 className="text-sm font-semibold text-red-800">Event Completed</h3>
                  <p className="text-xs text-red-700">{getReminderLockMessage(event)}</p>
                  <p className="text-xs text-red-700 mt-1">Sending reminders is no longer available.</p>
                </div>
              </div>
            </div>
          )}
          
          {/* Event details */}
          <div className="space-y-2">
            <h3 className="text-base font-medium">Event</h3>
            <div className="flex items-center gap-2">
              <div className="relative flex items-center justify-center w-9 h-9 rounded-full bg-[#F5F7FA]">
                <Calendar className="h-4 w-4 text-gray-500" />
              </div>
              <span className="text-sm">{event?.eventName}</span>
              <span className="text-sm text-gray-500 ml-auto">{FormatDate(event?.eventDate)}</span>
            </div>
          </div>
          {/* Message from host */}
          <div className="space-y-2">            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">Message by Host (Optional)</h3>
              {session?.user?.hasAiAccess && event?.message && (
                <Button
                  variant="primary-button"
                  size="sm"
                  onClick={handleGenerateMessage}
                  disabled={generatingMessage || isReminderLocked(event)}
                  className="text-xs"
                >
                  {generatingMessage ? (
                    <>
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-3 w-3 mr-1" />
                      Generate AI Message
                    </>
                  )}
                </Button>
              )}
            </div>            
            <Textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Add a personal message to your reminder"
              // className="resize-none"
            />
            {session?.user?.hasAiAccess && event?.message && (
              <p className="text-xs text-gray-500">
                Click &quot;Generate AI Message&quot; to create a reminder based on your original event message.
              </p>
            )}
          </div>

          {/* Total invites */}
          <div className="pt-2 space-y-1">
            <h3 className="text-base font-semibold">Total Invites: {totalInvites}</h3>
            <p className="text-sm text-gray-500">Invites with email: {invitesWithEmail}</p>
            {invitesWithEmail === 0 && (
              <div className="w-full py-2 px-4 bg-yellow-50 border border-yellow-100 text-center rounded-md text-sm">
                No invites with email addresses found
              </div>
            )}
            <p className="text-xs text-gray-500 italic">Note: Reminders will not be sent to declined invites</p>
          </div>
        </div>

        <DialogFooter className="flex sm:justify-between gap-2">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="flex-1"
            disabled={sending}
          >
            Cancel
          </Button>
          <Button
            variant="primary-button"
            onClick={handleSendReminder}
            className="flex-1"
            disabled={sending || invitesWithEmail === 0 || isReminderLocked(event)}
            title={isReminderLocked(event) ? (getReminderLockMessage(event) || "Reminders locked") : ""}
          >
            {sending ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Sending...
              </>
            ) : (
              <>
                <Mail className="h-4 w-4 mr-2" />
                Send Reminder
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
