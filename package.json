{"name": "iac", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "publish": "./scripts/push-image.sh production", "prestart": "pnpm run generate-favicon && ./scripts/build-env.sh", "start": "next dev", "serve": "next start -p 3000", "lint": "next lint", "generate-favicon": "node scripts/generate-favicon.js", "test:e2e": "cypress open", "test:e2e:ci": "cypress run", "test:e2e:dev": "cypress open --config-file cypress.config.ts", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@auth/firebase-adapter": "^2.8.0", "@aws-sdk/client-ses": "^3.758.0", "@google/genai": "^1.5.1", "@hookform/resolvers": "^4.1.3", "@next/third-parties": "^15.3.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@types/bcryptjs": "^3.0.0", "@types/formidable": "^3.4.5", "@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "firebase": "^11.4.0", "firebase-admin": "^13.2.0", "firebaseui": "^6.1.0", "formidable": "^3.5.2", "handlebars": "^4.7.8", "jszip": "^3.10.1", "lru-cache": "^11.1.0", "lucide-react": "^0.482.0", "mailersend": "^2.3.0", "micro": "^10.0.1", "mime": "^4.0.6", "next": "15.2.2", "next-auth": "^4.24.5", "next-seo": "^6.6.0", "nodemailer": "^6.10.0", "pdf-lib": "^1.17.1", "qrcode": "^1.5.4", "react": "19.0.0", "react-day-picker": "^8.10.1", "react-dom": "19.0.0", "react-hook-form": "^7.54.2", "react-qr-code": "^2.0.15", "react-qr-scanner": "1.0.0-alpha.11", "recharts": "^2.15.3", "sass": "^1.85.1", "stripe": "^17.7.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.4", "twig": "^1.17.1", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@badeball/cypress-cucumber-preprocessor": "^22.0.1", "@bahmutov/cypress-esbuild-preprocessor": "^2.2.4", "@chromatic-com/storybook": "^3", "@cloudflare/workers-types": "^4.20250601.0", "@storybook/addon-a11y": "^8.6.12", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-links": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/addon-postcss": "^2.0.0", "@storybook/blocks": "^8.6.12", "@storybook/experimental-addon-test": "^8.6.12", "@storybook/experimental-nextjs-vite": "8.6.12", "@storybook/nextjs": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/test": "^8.6.12", "@tailwindcss/postcss": "^4.0.17", "@types/next-auth": "^3.13.0", "@types/node": "^20.17.24", "@types/node-fetch": "^2.6.12", "@types/qrcode": "^1.5.5", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/recharts": "^2.0.1", "@types/twig": "^1.12.16", "@vitest/browser": "^3.1.3", "@vitest/coverage-v8": "^3.1.3", "autoprefixer": "^10.4.21", "css-loader": "^7.1.2", "cypress": "^14.2.1", "eslint": "^9.22.0", "eslint-config-next": "15.2.2", "eslint-plugin-storybook": "^0.12.0", "playwright": "^1.52.0", "postcss": "^8.5.3", "postcss-loader": "^8.1.1", "sharp": "^0.33.5", "storybook": "^8.6.12", "style-loader": "^4.0.0", "tailwindcss": "^4.0.17", "typescript": "^5.8.2", "vitest": "^3.1.3"}, "pnpm": {"onlyBuiltDependencies": ["@badeball/cypress-cucumber-preprocessor", "@firebase/util", "@parcel/watcher", "cypress", "esbuild", "protobufjs", "sharp"]}}