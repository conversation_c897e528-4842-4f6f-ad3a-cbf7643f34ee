import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { Database } from '@/lib/database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { eventId } = req.query;
    if (!eventId || Array.isArray(eventId)) {
      return res.status(400).json({ error: 'Event ID is required' });
    }

    const db = Database.getInstance();
    const event = await db.readData('events', eventId);

    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    // Check if user has access to this event
    if (event.ownerAccountId !== session.user.id && !event.managers?.includes(session.user.id)) {
      return res.status(403).json({ error: 'Access denied' });
    }

    return res.status(200).json({
      eventId: event.ID,
      eventName: event.eventName,
      organizationId: event.organizationId,
      ownerAccountId: event.ownerAccountId,
      ownerEmail: event.ownerEmail,
      sessionOrganizationId: session.user.organization?.id,
      sessionUserId: session.user.id,
      isOrganizationIdSet: !!event.organizationId,
      organizationIdMatchesSession: event.organizationId === session.user.organization?.id,
      organizationIdMatchesUserId: event.organizationId === session.user.id
    });

  } catch (error) {
    console.error('Error checking event organization:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
