import { Button } from "@/components/ui/button";
import { MoveIcon, SaveIcon, UploadIcon, EyeIcon, Sparkles, Loader2 as LoaderIcon } from "lucide-react";
import { LabelPreview } from "./labelPreview";
import { Input } from "./ui/input";
import { useEvent } from "@/hooks/useEvent";
import { InvitePreview } from "./invitePreview";
import { useState, useEffect, forwardRef, useImperativeHandle, useRef } from "react";
import { InvitePageSize, Orientation, PrintSettings } from "@/types";
import { useToast } from "@/components/ui/use-toast";
import { useSession } from "next-auth/react";
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface InvitationCardFormProps {
  eventId: string;
}

export const InvitationCardForm = forwardRef<
  { savePreferences: () => Promise<void> },
  InvitationCardFormProps
>(({ eventId }, ref) => {
  const { event, saveEvent } = useEvent(eventId as string);

  // State for all preference settings
  const [pageSize, setPageSize] = useState<InvitePageSize>("A4");
  const [orientation, setOrientation] = useState<Orientation>("portrait");
  const [horizontalPosition, setHorizontalPosition] = useState(50);
  const [verticalPosition, setVerticalPosition] = useState(50);
  const [bgColor, setBgColor] = useState("#FFFFFF");
  const [qrColor, setQrColor] = useState("#000000");
  const [labelOrientation, setLabelOrientation] = useState<Orientation>("portrait");  const [saving, setSaving] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [generatingAI, setGeneratingAI] = useState(false);
  const [inviteImage, setInviteImage] = useState<string | null>(null);
  const [previewKey, setPreviewKey] = useState<number>(Date.now()); // Add a key for refreshing the preview
  const [isPreviewSheetOpen, setIsPreviewSheetOpen] = useState(false);

  // State to track initial values for change detection
  const [initialValues, setInitialValues] = useState({
    pageSize: "A4" as InvitePageSize,
    orientation: "portrait" as Orientation,
    horizontalPosition: 50,
    verticalPosition: 50,
    bgColor: "#FFFFFF",
    qrColor: "#000000",
    labelOrientation: "portrait" as Orientation,
  });

  // References
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { data: session } = useSession();

  // Initialize state from existing event settings
  useEffect(() => {
    if (event?.printSettings?.inviteSettings) {
      const settings = event.printSettings.inviteSettings;
      const newPageSize = settings.pageSize || "A4";
      const newOrientation = settings.orientation || "portrait";
      const newHorizontalPosition = settings.labelPosition?.x || 50;
      const newVerticalPosition = settings.labelPosition?.y || 50;

      setPageSize(newPageSize);
      setOrientation(newOrientation);
      setHorizontalPosition(newHorizontalPosition);
      setVerticalPosition(newVerticalPosition);

      let newBgColor = "#FFFFFF";
      let newQrColor = "#000000";
      let newLabelOrientation: Orientation = "portrait";

      // If we have label settings
      if (event.printSettings.labelSettings) {
        newBgColor = event.printSettings.labelSettings.bgColor || "#FFFFFF";
        newQrColor = event.printSettings.labelSettings.qrColor || "#000000";
        newLabelOrientation = event.printSettings.labelSettings.orientation || "portrait";

        setBgColor(newBgColor);
        setQrColor(newQrColor);
        setLabelOrientation(newLabelOrientation);
      }

      // Set initial values for change tracking
      setInitialValues({
        pageSize: newPageSize,
        orientation: newOrientation,
        horizontalPosition: newHorizontalPosition,
        verticalPosition: newVerticalPosition,
        bgColor: newBgColor,
        qrColor: newQrColor,
        labelOrientation: newLabelOrientation,
      });
    }    // Check if there's an existing background image
    if (eventId) {
      fetch(`/api/storage/getUrl?eventId=${eventId}&imageType=invitation-card`)
        .then(response => response.json())
        .then(data => {
          if (data.exists && data.url) {
            // Add a cache-busting parameter to ensure fresh image
            const separator = data.url.includes('?') ? '&' : '?';
            const imageUrlWithCache = data.url + separator + 'cache=' + new Date().getTime();
            setInviteImage(imageUrlWithCache);
          }
        })
        .catch(error => {
          console.error("Error fetching event image:", error);
        });
    }
  }, [event, eventId]);

  // Handle file upload
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || !e.target.files[0] || !eventId) {
      return;
    }    const file = e.target.files[0];
    
    // Check file type
    if (file.type !== 'image/png' && file.type !== 'image/jpeg') {
      toast({
        title: "Invalid File Type",
        description: "Please upload a PNG or JPEG image",
        variant: "destructive"
      });
      return;
    }

    // Check file size (10MB limit)
    const maxFileSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxFileSize) {
      const fileSizeInMB = (file.size / (1024 * 1024)).toFixed(1);
      toast({
        title: "File Too Large",
        description: `File size is ${fileSizeInMB}MB. Please upload an image smaller than 10MB`,
        variant: "destructive"
      });
      return;
    }

    setUploading(true);

    try {      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append('file', file);
      formData.append('eventId', eventId);
      formData.append('imageType', 'invitation-card'); // Specify this is for invitation card

      // Add user info from session if available
      if (session?.user?.email) {
        formData.append('uploadedBy', session.user.email);
      }

      // Upload using our API endpoint
      const response = await fetch('/api/storage/upload', {
        method: 'POST',
        body: formData,
        headers: {
          'X-User-Email': session?.user?.email || 'anonymous'
        }
      });      if (!response.ok) {
        const errorData = await response.json();
        // Handle specific HTTP status codes
        if (response.status === 413) {
          throw new Error('File size exceeds the 10MB limit. Please upload a smaller image.');
        }
        throw new Error(errorData.error || 'Upload failed');
      }      const data = await response.json();
      if (!data.url) {
        throw new Error('No URL returned from upload API');
      }      // Verify the image can be loaded before updating the UI
      const img = new Image();
      img.onload = () => {
        // Add timestamp to prevent caching, but check if URL already has query parameters
        const separator = data.url.includes('?') ? '&' : '?';
        setInviteImage(data.url + separator + 't=' + Date.now());        // Simply update the preview key to force a re-render of the preview component
        setPreviewKey(Date.now());

        toast({
          title: "Upload Successful",
          description: "Your background image has been updated and will appear in the preview.",
        });
      };
      img.onerror = () => {
        // Retry after a short delay in case Firebase Storage needs time to process
        setTimeout(() => {
          const retryImg = new Image();
          retryImg.onload = () => {
            const separator = data.url.includes('?') ? '&' : '?';
            setInviteImage(data.url + separator + 't=' + Date.now());
            setPreviewKey(Date.now());
            
            toast({
              title: "Upload Successful",
              description: "Your background image has been updated and will appear in the preview.",
            });
          };
          retryImg.onerror = () => {
            setInviteImage(null);
            toast({
              title: "Warning",
              description: "Image uploaded but may not display correctly. Please refresh the page or try again.",
              variant: "destructive"
            });
          };
          retryImg.src = data.url;
        }, 2000); // Wait 2 seconds before retry
      };
      img.src = data.url;
    } catch (error) {
      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : "Could not upload the image. Please try again.",
        variant: "destructive"
      });
      setInviteImage(null);
    } finally {
      setUploading(false);
      // Clear the file input so the same file can be selected again if needed
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };
  // Trigger file input click
  const triggerFileUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Generate AI image based on event data
  const generateAIImage = async () => {
    if (!event) {
      toast({
        title: "Error",
        description: "Event data not loaded. Please refresh the page and try again.",
        variant: "destructive"
      });
      return;
    }

    setGeneratingAI(true);

    // Force recompilation
    try {
      const response = await fetch('/api/ai/generate-printable-invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventId: eventId, // Add the eventId from props
          eventName: event.eventName,
          eventDate: event.eventDate,
          startTime: event.start,
          endTime: event.end,
          location: event.location,
          timezone: event.timezone,
          host: event.host,
          message: event.message,
          messageStyle: 'personalised', // Default style, could be made configurable
          pageSize: pageSize,
          orientation: orientation
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate AI image');
      }

      if (!data.result || !data.result.imageData) {
        throw new Error('No image data received from AI generation');
      }

      // Convert base64 image data to blob and upload it
      const imageData = data.result.imageData;
      const mimeType = data.result.mimeType || 'image/png';
      
      // Create blob from base64 data
      const byteCharacters = atob(imageData);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: mimeType });

      // Create a file from the blob
      const file = new File([blob], `ai-generated-printable-invite-${Date.now()}.png`, { type: mimeType });

      // Upload using the same logic as manual upload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('eventId', eventId);
      formData.append('imageType', 'invitation-card');

      if (session?.user?.email) {
        formData.append('uploadedBy', session.user.email);
      }

      const uploadResponse = await fetch('/api/storage/upload', {
        method: 'POST',
        body: formData,
        headers: {
          'X-User-Email': session?.user?.email || 'anonymous'
        }
      });

      if (!uploadResponse.ok) {
        const errorData = await uploadResponse.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const uploadData = await uploadResponse.json();
      if (!uploadData.url) {
        throw new Error('No URL returned from upload API');
      }

      // Update the image and preview
      const separator = uploadData.url.includes('?') ? '&' : '?';
      setInviteImage(uploadData.url + separator + 't=' + Date.now());
      setPreviewKey(Date.now());

      toast({
        title: "AI Image Generated",
        description: `Your printable invite has been generated successfully for ${pageSize} ${orientation} format!`,
      });

    } catch (error) {
      console.error('Error generating AI image:', error);
      toast({
        title: "Generation Failed",
        description: error instanceof Error ? error.message : "Could not generate the AI image. Please try again.",
        variant: "destructive"
      });
    } finally {
      setGeneratingAI(false);
    }
  };

  // Check if any changes have been made
  const hasChanges = () => {
    return (
      pageSize !== initialValues.pageSize ||
      orientation !== initialValues.orientation ||
      horizontalPosition !== initialValues.horizontalPosition ||
      verticalPosition !== initialValues.verticalPosition ||
      bgColor !== initialValues.bgColor ||
      qrColor !== initialValues.qrColor ||
      labelOrientation !== initialValues.labelOrientation
    );
  };

  // Save preferences to event collection
  const savePreferences = async () => {
    if (!event) return;

    setSaving(true);

    try {
      // Create print settings object
      const printSettings: PrintSettings = {
        inviteSettings: {
          pageSize,
          orientation,
          labelPosition: {
            x: horizontalPosition,
            y: verticalPosition
          },
          labelScale: 1,
        },
        labelSettings: {
          bgColor,
          qrColor,
          orientation: labelOrientation
        }
      };

      // Update the event with the new print settings
      const updatedEvent = {
        ...event,
        printSettings
      };

      // Save the updated event
      await saveEvent(updatedEvent);

      // Update initial values to current values after successful save
      setInitialValues({
        pageSize,
        orientation,
        horizontalPosition,
        verticalPosition,
        bgColor,
        qrColor,
        labelOrientation,
      });

      // Show success toast
      toast({
        title: "Success",
        description: "Your preferences have been saved.",
      });
    } catch (error) {
      console.error("Error saving preferences:", error);

      // Show error toast
      toast({
        title: "Error",
        description: "Failed to save preferences. Please try again.",
        variant: "destructive"
      });

      throw error;
    } finally {
      setSaving(false);
    }
  };

  // Expose the savePreferences function to the parent component
  useImperativeHandle(ref, () => ({
    savePreferences
  }));

  // Reset to defaults
  const resetLabelSettings = () => {
    setBgColor("#FFFFFF");
    setQrColor("#000000");
    setLabelOrientation("portrait");

    // Update initial values to reflect the reset
    setInitialValues(prev => ({
      ...prev,
      bgColor: "#FFFFFF",
      qrColor: "#000000",
      labelOrientation: "portrait",
    }));
  };

  return (
    <div className="flex flex-1 items-stretch relative">
      <div className="w-full sm:flex-1 border-r lg:p-8 p-4 pb-20 sm:pb-8">
      {/* Form section */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-base font-semibold">Update Card</h2>
          <p className="text-xs text-gray-500">Customize the card&apos;s background, size, and orientation to match your
            preferences.</p>
        </div>
        <Button
          className="btn btn-primary hidden sm:flex"
          size={"sm"}
          variant={"outline"}
          onClick={savePreferences}
          disabled={saving || !hasChanges()}
        >
          <SaveIcon className="mr-1" /> {saving ? "Saving..." : "Save"}
        </Button>
      </div>
      <input
        aria-label="Background Image"
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept="image/png,image/jpeg"
        onChange={handleFileUpload}
        disabled={uploading}
      />      <Button
        variant={"outline"}
        className="mt-4 w-full"
        onClick={triggerFileUpload}
        disabled={uploading || generatingAI}
      >
        <span className="text-sm font-normal">
          {uploading ? "Uploading..." : (
            <>
              <UploadIcon className="inline mr-2" />
              {inviteImage ? "Change Background Image" : "Upload Background Image"}
            </>
          )}
        </span>
      </Button>

      {/* AI Generation Button */}
      {session?.user?.hasAiAccess && event && (
        <Button
          variant={"primary-button"}
          className="mt-2 w-full"
          onClick={generateAIImage}
          disabled={uploading || generatingAI}
        >
          {generatingAI ? (
            <>
              <LoaderIcon className="w-4 h-4 animate-spin mr-2" />
              <span className="text-sm">Generating {pageSize} {orientation} invite...</span>
            </>
          ) : (
            <>
              <Sparkles className="w-4 h-4 mr-2" />
              <span className="text-sm">Generate {pageSize} {orientation} invite with AI</span>
            </>
          )}
        </Button>
      )}

      <p className="text-xs text-gray-500 mt-1">
        PNG or JPEG images only. Maximum file size: 10MB
      </p>
      
      <div className="mt-6 grid grid-cols-2 gap-x-4 gap-y-6">
        {/* Row 1 */}
        <div className="flex flex-col justify-center">
          <label className="block text-sm font-medium text-foreground">Page size</label>
        </div>
        <Select
          value={pageSize}
          onValueChange={(value) => setPageSize(value as InvitePageSize)}
        >
          <SelectTrigger className="mt-1 w-full">
            <SelectValue placeholder="Select page size" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="A4">A4</SelectItem>
            <SelectItem value="A5">A5</SelectItem>
            <SelectItem value="A6">A6</SelectItem>
          </SelectContent>
        </Select>
        <div className="flex flex-col justify-center">
          <label className="block text-sm font-medium text-foreground">Orientation</label>
        </div>
        <Select
          value={orientation}
          onValueChange={(value) => setOrientation(value as Orientation)}
        >
          <SelectTrigger className="mt-1 w-full">
            <SelectValue placeholder="Select orientation" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="portrait">Portrait</SelectItem>
            <SelectItem value="landscape">Landscape</SelectItem>
          </SelectContent>
        </Select>

        <label className="block text-sm font-bold text-foreground">Adjust QR Label Position</label>
        <div className="text-right"><MoveIcon /></div>

        {/* Row 3 - Adjust QR Label Position */}
        <label className="block text-sm font-medium text-muted-foreground">Horizontal</label>
        <div className="mt-1 flex w-full items-center gap-2">
          <input
            aria-label="Horizontal Position"
            type="range"
            className="flex-1 text-sm"
            min="0"
            max="100"
            value={horizontalPosition}
            onChange={(e) => setHorizontalPosition(parseInt(e.target.value))}
          />
          <span className="text-xs font-medium text-gray-700 w-8 text-right">{horizontalPosition}%</span>
        </div>
        <label className="block text-sm font-medium text-muted-foreground">Vertical</label>
        <div className="mt-1 flex w-full items-center gap-2">
          <input
            aria-label="Vertical Position"
            type="range"
            className="flex-1 text-sm"
            min="0"
            max="100"
            value={verticalPosition}
            onChange={(e) => setVerticalPosition(parseInt(e.target.value))}
          />
          <span className="text-xs font-medium text-gray-700 w-8 text-right">{verticalPosition}%</span>
        </div>
      </div>
      <hr className="border-t border-gray-300 my-6" />
      <div className="flex gap-4">
        <div>
          <h2 className="text-base font-semibold">Update Label</h2>
          <p className="text-xs text-gray-500">Customize the background color, QR code color, and orientation
            of the QR label.</p>
        </div>
        <div className="ml-auto">
          <Button
            variant={"link"}
            size={"sm"}
            onClick={resetLabelSettings}
          >
            <span className="text-sm font-medium underline">Reset</span>
          </Button>
        </div>
      </div>
      <div className="flex w-full items-center gap-2 mt-4">
        <div className="flex-1">
          <label className="block text-sm font-medium text-foreground">Background Color</label>
          <div className="flex items-center gap-2 my-2">
            <Input
              type="color"
              className="w-12 h-8 border border-gray-300 rounded"
              value={bgColor}
              onChange={(e) => setBgColor(e.target.value)}
            />
            <Input
              type="text"
              className="flex-1 h-8 rounded"
              value={bgColor}
              onChange={(e) => setBgColor(e.target.value)}
            />
          </div>

          <label className="block text-sm font-medium foreground">QR Code Color</label>
          <div className="flex items-center gap-2 my-2">
            <Input
              type="color"
              className="w-12 h-8 border border-gray-300 rounded"
              value={qrColor}
              onChange={(e) => setQrColor(e.target.value)}
            />
            <Input
              type="text"
              className="flex-1 h-8 rounded"
              value={qrColor}
              onChange={(e) => setQrColor(e.target.value)}
            />
          </div>

          <label className="block text-sm font-medium text-foreground">Orientation</label>
          <Select
            value={labelOrientation}
            onValueChange={(value) => setLabelOrientation(value as Orientation)}
          >
            <SelectTrigger className="mt-1 w-full">
              <SelectValue placeholder="Select orientation" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="portrait">Portrait</SelectItem>
              <SelectItem value="landscape">Landscape</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex-1 justify-center items-center flex">
          <LabelPreview
            name={event?.eventName || "Event Name"}
            qrContent={`https://iamcoming.io/event/${eventId}`}
            orientation={labelOrientation}
            bgQr={bgColor}
            colorQr={qrColor}
          />
        </div>
      </div>
    </div>
    {/* Preview Section - Hidden on mobile */}
    <div className="hidden sm:flex sm:flex-1 flex-col">
      <div className="flex border-b px-4 py-2">
        <h2 className="text-sm font-medium">Preview</h2>
      </div>
      <div className="flex-1 justify-center items-center flex bg-gray-100 inset-shadow-sm p-8">
        {eventId && <InvitePreview
          key={previewKey} // Add key to force re-render when image changes
          eventId={eventId}
          size={pageSize}
          orientation={orientation}
          qrxp={horizontalPosition.toString()}
          qryp={verticalPosition.toString()}
          name={event?.eventName || "Event Name"}
          bgColor={bgColor}
          qrColor={qrColor}
          labelOrientation={labelOrientation}
        />}
      </div>
    </div>

    {/* Mobile Sticky Footer */}
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-4 sm:hidden z-10">
      <div className="flex gap-3">
        <Button
          className="flex-1"
          variant="outline"
          onClick={savePreferences}
          disabled={saving || !hasChanges()}
        >
          <SaveIcon className="mr-2 h-4 w-4" />
          {saving ? "Saving..." : "Save"}
        </Button>
        <Button
          className="flex-1"
          variant="primary-button"
          onClick={() => setIsPreviewSheetOpen(true)}
        >
          <EyeIcon className="mr-2 h-4 w-4" />
          Preview
        </Button>
      </div>
    </div>

    {/* Mobile Preview Sheet */}
    <Sheet open={isPreviewSheetOpen} onOpenChange={setIsPreviewSheetOpen}>
      <SheetContent side="bottom" className="h-[80vh] p-0">
        <SheetHeader className="p-4 border-b">
          <SheetTitle>Invitation Preview</SheetTitle>
        </SheetHeader>
        <div className="flex-1 flex justify-center items-start bg-gray-100 p-4 h-full overflow-auto">
          {eventId && (
            <div className="w-full flex justify-center">
              <div className="relative w-full max-w-sm">
                <InvitePreview
                  key={previewKey}
                  eventId={eventId}
                  size={pageSize}
                  orientation={orientation}
                  qrxp={horizontalPosition.toString()}
                  qryp={verticalPosition.toString()}
                  name={event?.eventName || "Event Name"}
                  bgColor={bgColor}
                  qrColor={qrColor}
                  labelOrientation={labelOrientation}
                  fullSize={true}
                />
              </div>
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  </div>);
});

// Add display name to resolve the ESLint error
InvitationCardForm.displayName = "InvitationCardForm";