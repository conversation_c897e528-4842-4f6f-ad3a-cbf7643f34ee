import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import geminiService from '@/lib/gemini/service';
import { Database } from '@/lib/database';
import { StringArrayToString } from '@/lib/utils';
import { Event, EventFormData } from '@/types';
import { checkAIUsageLimit, incrementAIUsage } from '@/lib/ai-usage';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  try {
    const { eventId } = req.query;
    if (!eventId || typeof eventId !== 'string') {
      return res.status(400).json({ error: 'Event ID is required' });
    }

    const normalizedEventId = StringArrayToString(eventId);

    // Get session for user authentication
    const session = await getServerSession(req, res, authConfig);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if user has AI access
    if (!session.user.hasAiAccess) {
      return res.status(403).json({ error: 'AI access not available for this user' });
    }

    // Check AI usage limits
    try {
      const limitCheck = await checkAIUsageLimit(normalizedEventId);

      if (!limitCheck.canUseAI) {
        return res.status(429).json({
          error: 'AI usage limit exceeded',
          details: limitCheck.message,
          currentUsage: limitCheck.currentUsage,
          limit: limitCheck.limit
        });
      }
    } catch (error) {
      console.error('Error checking AI usage limit:', error);
      return res.status(500).json({ error: 'Failed to check AI usage limits' });
    }

    // Get the event from database
    const event = await Database.getInstance().readData('events', Database.normalizeId(normalizedEventId)) as Event;
    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    // Check if event has a message to base the reminder on
    if (!event.message) {
      return res.status(400).json({ error: 'Event does not have an original message to base the reminder on' });
    }

    // Prepare event data for the AI service
    const eventFormData: EventFormData = {
      eventName: event.eventName,
      eventDate: event.eventDate,
      start: event.start,
      end: event.end,
      location: event.location,
      host: event.host,
      message: event.message
    };

    // Generate reminder message using Gemini
    const reminderMessage = await geminiService.generateReminderMessage(event.message, eventFormData);

    // Record AI usage after successful generation
    await incrementAIUsage(normalizedEventId);

    return res.status(200).json({
      success: true,
      reminderMessage,
      originalMessage: event.message,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error generating reminder message:', error);
    return res.status(500).json({ 
      error: 'Failed to generate reminder message',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
