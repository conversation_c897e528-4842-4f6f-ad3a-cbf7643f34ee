import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import geminiService from '@/lib/gemini/service';
import { Database } from '@/lib/database';
import { StringArrayToString } from '@/lib/utils';
import { Event, EventFormData } from '@/types';
import { withAILimitCheck, recordAIUsage } from '@/lib/ai-middleware';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  try {
    const { eventId } = req.query;
    if (!eventId || typeof eventId !== 'string') {
      return res.status(400).json({ error: 'Event ID is required' });
    }

    const normalizedEventId = StringArrayToString(eventId);

    // Check AI usage limits and permissions
    const limitResult = await withAILimitCheck(req, res, normalizedEventId);
    if (!limitResult.canProceed) {
      const error = limitResult.error!;
      return res.status(error.status).json(error.json);
    }

    // Get the event from database
    const event = await Database.getInstance().readData('events', Database.normalizeId(normalizedEventId)) as Event;
    if (!event) {
      return res.status(404).json({ error: 'Event not found' });
    }

    // Check if event has a message to base the reminder on
    if (!event.message) {
      return res.status(400).json({ error: 'Event does not have an original message to base the reminder on' });
    }

    // Prepare event data for the AI service
    const eventFormData: EventFormData = {
      eventName: event.eventName,
      eventDate: event.eventDate,
      start: event.start,
      end: event.end,
      location: event.location,
      host: event.host,
      message: event.message
    };

    // Generate reminder message using Gemini
    const reminderMessage = await geminiService.generateReminderMessage(event.message, eventFormData);

    // Record AI usage after successful generation
    await recordAIUsage(normalizedEventId);

    return res.status(200).json({
      success: true,
      reminderMessage,
      originalMessage: event.message,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error generating reminder message:', error);
    return res.status(500).json({ 
      error: 'Failed to generate reminder message',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
