import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import geminiService from '@/lib/gemini/service';
import { EventFormData } from '@/types';
import { withAILimitCheck, recordAIUsage } from '@/lib/ai-middleware';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      eventId,
      eventName,
      eventDate,
      startTime,
      endTime,
      location,
      timezone,
      host,
      message,
      messageStyle = 'personalised'
    } = req.body;

    // Validate required fields
    if (!eventName) {
      return res.status(400).json({ error: 'Event name is required' });
    }

    if (!eventId) {
      return res.status(400).json({ error: 'Event ID is required' });
    }

    // Check AI usage limits and permissions
    const limitResult = await withAILimitCheck(req, res, eventId);
    if (!limitResult.canProceed) {
      const error = limitResult.error!;
      return res.status(error.status).json(error.json);
    }

    // Validate messageStyle if provided
    const validStyles = ['personalised', 'casual_friendly', 'formal_professional', 'fun_energetic', 'business_professional', 'creative_unique'];
    if (messageStyle && !validStyles.includes(messageStyle)) {
      return res.status(400).json({ 
        error: `Invalid message style. Must be one of: ${validStyles.join(', ')}` 
      });
    }

    // Prepare event data for AI image generation
    const eventData: EventFormData = {
      eventName,
      eventDate: eventDate ? new Date(eventDate) : new Date(),
      start: startTime || '09:00',
      end: endTime || '17:00',
      location: location || '',
      timezone: timezone,
      host: host || '',
      message: message,
      messageStyle: messageStyle
    };

    // Generate digital invite image using Gemini service
    const result = await geminiService.generateDigitalInviteImage(eventData);

    // Record AI usage after successful generation
    await recordAIUsage(eventId);

    return res.status(200).json({
      success: true,
      result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error generating digital invite image with AI:', error);
    return res.status(500).json({ 
      error: 'Failed to generate digital invite image with AI',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
